package br.com.alice.appointment.controller

import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.APPOINTMENT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.TimelineType
import br.com.alice.data.layer.services.TimelineDataService
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

class BackfillTimelineController(
    private val timelineDataService: TimelineDataService,
) : Controller() {

    suspend fun changeType(request: TimelineChangeTypeRequest): Response = withEnvironment {
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        timelineDataService.find { where { this.referencedModelId.inList(request.timelineIds) and this.type.eq(request.oldType) } }
            .pmapEach { timeline ->
                runCatching {
                    timelineDataService.update(timeline.copy(type = request.newType)).get()
                    successCount.incrementAndGet()
                }.onFailure {
                    errorsCount.incrementAndGet()
                    errors["timeline ${timeline.id} with error"] = it.message ?: "Unknown error"
                }
            }

        InternalFeatureResponse(
            successCount = successCount.get(),
            errorsCount = errorsCount.get(),
            additionalInfo = errors
        ).toResponse()
    }
    private suspend fun withEnvironment(function: suspend () -> Response) =
        withRootServicePolicy(APPOINTMENT_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(APPOINTMENT_ROOT_SERVICE_NAME) {
                asyncLayer {
                    function()
                }
            }
        }
}

data class TimelineChangeTypeRequest(
    val newType: TimelineType,
    val oldType: TimelineType,
    val timelineIds: List<UUID>
)
