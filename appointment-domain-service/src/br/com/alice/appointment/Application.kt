package br.com.alice.appointment

import br.com.alice.akinator.ioc.AkinatorDomainClientModule
import br.com.alice.appointment.client.AppointmentDocumentPrinter
import br.com.alice.appointment.client.AppointmentEventService
import br.com.alice.appointment.client.AppointmentEvolutionService
import br.com.alice.appointment.client.AppointmentExternalFilesService
import br.com.alice.appointment.client.AppointmentMacroService
import br.com.alice.appointment.client.AppointmentProcedureExecutedService
import br.com.alice.appointment.client.AppointmentService
import br.com.alice.appointment.client.AppointmentTemplateService
import br.com.alice.appointment.client.AttendantsTimelineService
import br.com.alice.appointment.client.SuggestedProcedureService
import br.com.alice.appointment.client.TimelineAiSummaryReviewService
import br.com.alice.appointment.client.TimelineService
import br.com.alice.appointment.consumers.AppointmentAISummaryConsumer
import br.com.alice.appointment.consumers.AppointmentConsumer
import br.com.alice.appointment.consumers.AppointmentEventConsumer
import br.com.alice.appointment.consumers.AppointmentEvolutionConsumer
import br.com.alice.appointment.consumers.AppointmentExcuseNoteConsumer
import br.com.alice.appointment.consumers.AppointmentProcedureExecutedConsumer
import br.com.alice.appointment.consumers.AppointmentProcedureExecutedGroupConsumer
import br.com.alice.appointment.consumers.BackfillConsumer
import br.com.alice.appointment.consumers.CaseRecordConsumer
import br.com.alice.appointment.consumers.ChannelEventConsumer
import br.com.alice.appointment.consumers.CounterReferralConsumer
import br.com.alice.appointment.consumers.HealthFormConsumer
import br.com.alice.appointment.consumers.HealthPlanTaskGroupPublishedConsumer
import br.com.alice.appointment.consumers.RefundCounterReferralConsumer
import br.com.alice.appointment.consumers.SpecialistOpinionConsumer
import br.com.alice.appointment.consumers.TertiaryIntentionTouchPointConsumer
import br.com.alice.appointment.consumers.ThirdPartyAppointmentConsumer
import br.com.alice.appointment.controller.AppointmentBackfillController
import br.com.alice.appointment.controller.BackfillCaseRecordDetailsAppointmentController
import br.com.alice.appointment.controller.BackfillNotifyAppointmentCompletedController
import br.com.alice.appointment.controller.BackfillPedrinhoController
import br.com.alice.appointment.controller.BackfillReferralAppointmentEventController
import br.com.alice.appointment.controller.BackfillSuggestedProcedureController
import br.com.alice.appointment.controller.BackfillTimelineController
import br.com.alice.appointment.controller.InternalFeatureController
import br.com.alice.appointment.routes.apiRoutes
import br.com.alice.appointment.routes.kafkaRoutes
import br.com.alice.appointment.services.AppointmentDocumentPrinterImpl
import br.com.alice.appointment.services.AppointmentEventServiceImpl
import br.com.alice.appointment.services.AppointmentEvolutionServiceImpl
import br.com.alice.appointment.services.AppointmentExternalFilesServiceImpl
import br.com.alice.appointment.services.AppointmentMacroServiceImpl
import br.com.alice.appointment.services.AppointmentProcedureExecutedServiceImpl
import br.com.alice.appointment.services.AppointmentServiceImpl
import br.com.alice.appointment.services.AppointmentTemplateServiceImpl
import br.com.alice.appointment.services.AttendantsTimelineServiceImpl
import br.com.alice.appointment.services.SuggestedProcedureServiceImpl
import br.com.alice.appointment.services.TimelineAiSummaryReviewServiceImpl
import br.com.alice.appointment.services.TimelineServiceImpl
import br.com.alice.appointment.services.internal.AppointmentAutofillServiceImpl
import br.com.alice.appointment.services.internal.AppointmentInternalService
import br.com.alice.appointment.services.internal.AppointmentNotificationService
import br.com.alice.appointment.services.internal.ExcuseNotesService
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.channel.ioc.ChannelDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.data.layer.APPOINTMENT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.*
import br.com.alice.documentsigner.ioc.DocumentSignerModule
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = DocumentSignerModule + listOf(
        KafkaProducerModule,
        StaffDomainClientModule,
        ProviderDomainClientModule,
        EhrDomainClientModule,
        FeatureConfigDomainClientModule,
        ChannelDomainClientModule,
        HealthLogicDomainClientModule,
        FileVaultClientModule,
        HealthConditionDomainClientModule,
        AkinatorDomainClientModule,
        HealthPlanDomainClientModule,
        ExecIndicatorDomainClientModule,
        ClinicalAccountDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }

            // Exposed Services
            single<AppointmentService> {
                AppointmentServiceImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single<AppointmentEvolutionService> { AppointmentEvolutionServiceImpl(get(), get(), get()) }
            single<AppointmentEventService> { AppointmentEventServiceImpl(get()) }
            single<AppointmentMacroService> { AppointmentMacroServiceImpl(get()) }
            single<AppointmentTemplateService> { AppointmentTemplateServiceImpl(get()) }
            single<TimelineService> { TimelineServiceImpl(get(), get()) }
            single<AttendantsTimelineService> { AttendantsTimelineServiceImpl(get(), get()) }
            single<AppointmentExternalFilesService> { AppointmentExternalFilesServiceImpl(get(), get()) }
            single<TimelineAiSummaryReviewService> { TimelineAiSummaryReviewServiceImpl(get()) }
            single<AppointmentProcedureExecutedService> { AppointmentProcedureExecutedServiceImpl(get(), get()) }
            single<SuggestedProcedureService> { SuggestedProcedureServiceImpl(get()) }
            single<AppointmentDocumentPrinter> {
                AppointmentDocumentPrinterImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }

            // Service Server
            loadServiceServers("br.com.alice.appointment.services")

            // Data Services
            val invoker = DataLayerClientConfiguration.build(DefaultHttpClient(timeoutInMillis = 10_000))
            single<AppointmentDataService> { AppointmentDataServiceClient(invoker) }
            single<AppointmentEvolutionDataService> { AppointmentEvolutionDataServiceClient(invoker) }
            single<AppointmentEventDataService> { AppointmentEventDataServiceClient(invoker) }
            single<AppointmentMacroDataService> { AppointmentMacroDataServiceClient(invoker) }
            single<AppointmentTemplateDataService> { AppointmentTemplateDataServiceClient(invoker) }
            single<CounterReferralDataService> { CounterReferralDataServiceClient(invoker) }
            single<TimelineDataService> { TimelineDataServiceClient(invoker) }
            single<TimelineAiSummaryReviewDataService> { TimelineAiSummaryReviewDataServiceClient(invoker) }
            single<AppointmentProcedureExecutedDataService> { AppointmentProcedureExecutedDataServiceClient(invoker) }
            single<SuggestedProcedureDataService> { SuggestedProcedureDataServiceClient(invoker) }

            // Controllers
            single { HealthController(APPOINTMENT_ROOT_SERVICE_NAME) }
            single { InternalFeatureController(get(), get(), get()) }
            single { BackfillPedrinhoController(get()) }
            single { BackfillNotifyAppointmentCompletedController(get(), get()) }
            single { BackfillCaseRecordDetailsAppointmentController(get(), get(), get()) }
            single { BackfillSuggestedProcedureController(get(), get()) }
            single { BackfillReferralAppointmentEventController(get(), get()) }
            single { BackfillTimelineController(get()) }
            single { AppointmentBackfillController(get(), get(), get()) }


            // Internal Service
            single { AppointmentNotificationService(get()) }
            single { AppointmentInternalService(get(), get(), get(), get()) }
            single { ExcuseNotesService(get(), get(), get()) }
            single { AppointmentAutofillServiceImpl(get())}

            //Consumers
            single { ThirdPartyAppointmentConsumer(get()) }
            single { AppointmentConsumer(get(), get(), get(), get()) }
            single { TertiaryIntentionTouchPointConsumer(get()) }
            single { CounterReferralConsumer(get(), get(), get()) }
            single { HealthFormConsumer(get()) }
            single { HealthPlanTaskGroupPublishedConsumer(get()) }
            single { AppointmentEvolutionConsumer(get()) }
            single { SpecialistOpinionConsumer(get()) }
            single { AppointmentEventConsumer(get(), get()) }
            single { CaseRecordConsumer(get()) }
            single { RefundCounterReferralConsumer(get()) }
            single { ChannelEventConsumer(get<AppointmentService>() as AppointmentServiceImpl) }
            single { AppointmentExcuseNoteConsumer(get()) }
            single { AppointmentAISummaryConsumer(get(), get(), get()) }
            single { AppointmentProcedureExecutedConsumer(get()) }
            single { AppointmentProcedureExecutedGroupConsumer(get()) }
            single { BackfillConsumer(get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
    startRoutesSync: Boolean = true
) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, APPOINTMENT_ROOT_SERVICE_NAME)
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(FeatureNamespace.APPOINTMENT)
    }
}
