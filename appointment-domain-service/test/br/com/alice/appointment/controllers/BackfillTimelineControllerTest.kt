package br.com.alice.appointment.controllers

import br.com.alice.appointment.controller.BackfillTimelineController
import br.com.alice.appointment.controller.InternalFeatureResponse
import br.com.alice.appointment.controller.TimelineChangeTypeRequest
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TimelineType
import br.com.alice.data.layer.services.TimelineDataService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test


class BackfillTimelineControllerTest : RoutesTestHelper() {

    private val dataService: TimelineDataService = mockk()
    private val controller = BackfillTimelineController(
        dataService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `changeType should update type at timeline`() {
        val timeline = TestModelFactory.buildTimeline()
        val timelineError = TestModelFactory.buildTimeline()
        val request = TimelineChangeTypeRequest(
            oldType = TimelineType.CLINICAL_RECORD,
            newType = TimelineType.APPOINTMENT_COMMUNITY,
            timelineIds = listOf(timeline.id, timelineError.id)
        )

        val expected = InternalFeatureResponse(
            successCount = 1,
            errorsCount = 1,
            additionalInfo = mapOf(
                "timeline ${timelineError.id} with error" to "Error"
            )
        )

        coEvery {
            dataService.find(queryEq { this.where { referencedModelId.inList(request.timelineIds) and type.eq(request.oldType) } })
        } returns listOf(timeline, timelineError).success()

        coEvery {
            dataService.update(timeline.copy(type = request.newType))
        } returns timeline
        coEvery {
            dataService.update(timelineError.copy(type = request.newType))
        } returns Exception("Error").failure()

        post("/backfill/change_timeline_type", body = request) {
            assertThat(it).isOKWithData(expected)

            coVerifyOnce { dataService.find(any()) }
            coVerify(exactly = 2) { dataService.update(any()) }
        }
    }

}
