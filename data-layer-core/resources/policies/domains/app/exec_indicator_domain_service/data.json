{"rules": [{"conditions": [], "allow": [{"resources": ["PersonModel", "ExecIndicatorAuthorizerModel", "HealthcareTeamModel", "MemberModel", "PersonClinicalAccount", "StaffModel", "ProviderModel", "HealthProfessionalModel", "MedicalSpecialtyModel", "ProviderUnitModel", "StructuredAddress", "GlossAuthorizationInfoModel", "HealthcareResourceGroupAssociationModel", "FileVault", "NullvsIntegrationRecordModel", "Appointment", "HealthCommunitySpecialistModel", "Generic<PERSON><PERSON><PERSON><PERSON>"], "actions": ["view"]}, {"resources": ["HealthcareBundleModel"], "actions": ["view", "update"]}, {"resources": ["ExecIndicatorEventModel", "EligibilityCheckModel", "TotvsGuiaModel", "HealthInstitutionNegotiationModel"], "actions": ["view", "update", "create"]}, {"resources": ["AppointmentProcedureExecutedAliceCodeSourceModel"], "actions": ["view", "create"]}, {"resources": ["ExecutionGroupModel"], "actions": ["view", "update", "create", "delete"]}, {"resources": ["MvAuthorizedProcedureModel", "AttachmentOpmeModel", "GuiaWithProceduresModel", "HospitalizationInfoModel", "AttachmentChemotherapyModel", "AttachmentRadiotherapyModel", "HealthSpecialistResourceBundleModel", "ResourceBundleSpecialtyModel", "ResourceBundleSpecialtyPricingModel", "ResourceBundleSpecialtyPricingUpdateModel"], "actions": ["view", "count", "update", "create"]}, {"resources": ["HealthcareResourceModel"], "actions": ["view", "count", "update"]}, {"resources": ["ProductModel", "ProductBundleModel", "PriceListingModel", "ProductPriceListingModel", "CompanyProductPriceListingModel", "ProductGroupModel", "HealthCondition"], "actions": ["view", "count"]}, {"_comment": "Used in backfill", "resources": ["ProviderUnitGroupModel"], "actions": ["view"]}, {"_comment": "Used in backfill", "resources": ["TussProcedureSpecialtyModel", "HealthcareResourceGroupAssociationModel"], "actions": ["view", "update", "create"]}, {"_comment": "Used in backfill", "resources": ["HealthSpecialistResourceBundleModel"], "actions": ["delete"]}]}, {"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["MvAuthorizedProcedureModel", "MagicNumbersModel"], "actions": ["view", "update"]}, {"resources": ["ExecIndicatorAuthorizerModel", "ExecutionGroupModel", "TotvsGuiaModel", "StaffModel", "HealthProfessionalModel", "ContactModel"], "actions": ["view"]}, {"_comment": "Used in backfill", "resources": ["MvAuthorizedProcedureModel", "TotvsGuiaModel"], "actions": ["delete"]}, {"_comment": "Used in backfill", "resources": ["ExecutionGroupModel"], "actions": ["count", "update"]}, {"_comment": "Used in backfill", "resources": ["HealthEventsModel", "HealthcareResourceGroupModel"], "actions": ["view"]}, {"_comment": "Used in backfill", "resources": ["GlossAuthorizationInfoModel"], "actions": ["view", "update", "create"]}], "branches": [{"conditions": ["${subject.key} == ${resource.referencedModelId}"], "allow": [{"resources": ["PersonHealthEvent"], "actions": ["view", "create", "update"]}]}]}]}