package br.com.alice.exec.indicator.service.internal


import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.services.ResourceBundleSpecialtyPricingModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.isSuccess
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID


class ResourceBundleSpecialtyPricingService(
    private val resourceBundleSpecialtyPricingModelDataService: ResourceBundleSpecialtyPricingModelDataService,
) {
    suspend fun getCurrentlyActiveByResourceBundleSpecialtyId(
        resourceBundleSpecialtyIds: List<UUID>
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.find {
            where {
                resourceBundleSpecialtyId.inList(resourceBundleSpecialtyIds)
            }
        }.mapEach {
            it.toTransport()
        }.map {
            it.filter {
                it.beginAt <= LocalDate.now() && (it.endAt == null || it.endAt!! >= LocalDate.now())
            }
        }

    suspend fun getCurrentlyActiveAndFuturePricesByResourceBundleSpecialtyId(
        resourceBundleSpecialtyIds: List<UUID>
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.find {
            where {
                resourceBundleSpecialtyId.inList(resourceBundleSpecialtyIds)
            }
        }.mapEach {
            it.toTransport()
        }.map {
            it.filter {
                (it.beginAt <= LocalDate.now() && (it.endAt == null || it.endAt!! >= LocalDate.now())) ||
                    it.beginAt > LocalDate.now()
            }
        }

    suspend fun getActiveByResourceBundleSpecialtyIdAndEffectiveDate(
        resourceBundleSpecialtyIds: List<UUID>,
        effectiveDate: LocalDate,
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.find {
            where {
                resourceBundleSpecialtyId.inList(resourceBundleSpecialtyIds)
            }
        }.mapEach {
            it.toTransport()
        }.map {
            it.filter {
                it.beginAt <= effectiveDate && (it.endAt == null || it.endAt!! >= effectiveDate)
            }
        }

    suspend fun getMostRecentByResourceBundleSpecialtyId(
        resourceBundleSpecialtyIdParam: UUID
    ): Result<ResourceBundleSpecialtyPricing, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.findOne {
            where {
                resourceBundleSpecialtyId.eq(resourceBundleSpecialtyIdParam).and(
                    endAt.isNull()
                )
            }
        }.map {
            it.toTransport()
        }

    suspend fun add(specialty: ResourceBundleSpecialtyPricing) =
        resourceBundleSpecialtyPricingModelDataService.add(specialty.toModel()).map { it.toTransport() }

    suspend fun update(specialty: ResourceBundleSpecialtyPricing) =
        resourceBundleSpecialtyPricingModelDataService.update(specialty.toModel()).map { it.toTransport() }

    suspend fun updateList(
        specialties: List<ResourceBundleSpecialtyPricing>
    ): Result<List<ResourceBundleSpecialtyPricing>, Throwable> =
        resourceBundleSpecialtyPricingModelDataService.updateList(specialties.map { it.toModel() }).mapEach {
            it.toTransport()
        }

    suspend fun deleteList(
        specialties: List<ResourceBundleSpecialtyPricing>
    ): Result<List<Boolean>, Throwable> {
        return specialties.pmap {
            resourceBundleSpecialtyPricingModelDataService.delete(it.toModel())
        }.filter { it.isSuccess() }
            .pmap { it.get() }
            .success()
    }
}
