package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.Timeline
import br.com.alice.data.layer.models.TimelineReferenceModel
import br.com.alice.data.layer.models.TimelineStatus
import br.com.alice.data.layer.models.TimelineType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface TimelineDataService : Service,
    Finder<TimelineDataService.FieldOptions, TimelineDataService.OrderingOptions, Timeline>,
    Counter<TimelineDataService.FieldOptions, TimelineDataService.OrderingOptions, Timeline>,
    Adder<Timeline>,
    Updater<Timeline>,
    Deleter<Timeline>,
    UpdaterList<Timeline>,
    Getter<Timeline> {

    override val namespace: String
        get() = "appointment"
    override val serviceName: String
        get() = "timeline"


    class ReferencedModelDate : Field.DateTimeField(Timeline::referencedModelDate)
    class ReferencedModelId : Field.UUIDField(Timeline::referencedModelId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ReferencedModelClass : Field.TextField(Timeline::referencedModelClass) {
        fun eq(value: TimelineReferenceModel) = Predicate.eq(this, value)
        fun diff(value: TimelineReferenceModel) = Predicate.diff(this, value)
        fun inList(value: List<TimelineReferenceModel>) = Predicate.inList(this, value)
    }

    class ChannelIds : Field.JsonbField(Timeline::channelIds) {
        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<String>) = Predicate.containsAny(this, value)
    }

    class Status : Field.TextField(Timeline::status) {
        fun eq(value: TimelineStatus) = Predicate.eq(this, value)
        fun inList(value: List<TimelineStatus>) = Predicate.inList(this, value)
    }

    class Type : Field.TextField(Timeline::type) {
        fun eq(value: TimelineType) = Predicate.eq(this, value)
        fun inList(value: List<TimelineType>) = Predicate.inList(this, value)
    }

    class StaffId : Field.UUIDField(Timeline::staffId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class SpecialtyId : Field.UUIDField(Timeline::specialtyId) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PersonId : Field.TableIdField(Timeline::personId)

    class FieldOptions {
        val referencedModelId = ReferencedModelId()
        val referencedModelClass = ReferencedModelClass()
        val channelIds = ChannelIds()
        val specialtyId = SpecialtyId()
        val personId = PersonId()
        val type = Type()
        val staffId = StaffId()
        val status = Status()
    }

    class OrderingOptions {
        val referencedModelDate = ReferencedModelDate()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<Timeline, Throwable>
    override suspend fun add(model: Timeline): Result<Timeline, Throwable>
    override suspend fun update(model: Timeline): Result<Timeline, Throwable>
    override suspend fun updateList(models: List<Timeline>, returnOnFailure: Boolean): Result<List<Timeline>, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<Timeline>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun delete(model: Timeline): Result<Boolean, Throwable>
}
