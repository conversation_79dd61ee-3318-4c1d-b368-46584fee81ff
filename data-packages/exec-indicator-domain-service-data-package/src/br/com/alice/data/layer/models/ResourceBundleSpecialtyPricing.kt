package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.models.SpecialistTier
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class ResourceBundleSpecialtyPricing(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val resourceBundleSpecialtyId: UUID,
    val beginAt: LocalDate,
    val endAt: LocalDate? = null,
    val prices: List<ResourceBundleSpecialtyPrice> = emptyList(),
) : Model {
    companion object {
        val requiredCombinations = mapOf(
            SpecialistTier.ULTRA_EXPERT to listOf(TierType.TIER_0),
            SpecialistTier.SUPER_EXPERT to listOf(TierType.TIER_0, TierType.TIER_1),
            SpecialistTier.EXPERT to listOf(TierType.TIER_0, TierType.TIER_1, TierType.TIER_2),
            SpecialistTier.TALENTED to listOf(TierType.TIER_0, TierType.TIER_1, TierType.TIER_2, TierType.TIER_3)
        )
    }

    fun getPriceByTierAndProductTier(
        tier: SpecialistTier,
        productTier: TierType,
    ) = format(prices.firstOrNull { it.tier == tier && it.productTier == productTier }?.price ?: BigDecimal.ZERO)

    fun hasRequiredPrices(): Boolean {
        val priceSet = prices.map { it.tier to it.productTier }.toSet()

        return requiredCombinations.all { (tier, productTiers) ->
            productTiers.all { pt -> tier to pt in priceSet }
        }
    }

    fun numberOfMissingRequiredPrices(): Int {
        val priceSet = prices.map { it.tier to it.productTier }.toSet()

        return requiredCombinations.entries.sumOf { (tier, productTiers) ->
            productTiers.count { pt -> tier to pt !in priceSet }
        }
    }

    private fun format(value: BigDecimal) = value.setScale(2, RoundingMode.HALF_EVEN)

    fun expire() = copy(
        endAt = LocalDate.now(),
    )

}


data class ResourceBundleSpecialtyPrice(
    val tier: SpecialistTier,
    val productTier: TierType,
    val price: BigDecimal,
)
